<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CalendarController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Calendar API Routes
Route::middleware(['web', 'auth', 'verified'])->group(function () {
    Route::get('/calendar/{store_slug}/{month}', [CalendarController::class, 'calendar'])->name('api.calendar');
});
