import { ref, onMounted, onUnmounted, type Ref } from 'vue';

export interface CalendarData {
    date: string;
    day: string;
    url: string;
    total: number;
    bebas: number;
    diambil: number;
    terkirim: number;
    selesai: number;
    batal: number;
    overtime: number;
    bbro: number;
}

export interface CalendarApiResponse {
    data: CalendarData[];
    sum_total_job: number;
    sum_total_bebas: number;
    sum_total_diambil: number;
    sum_total_terkirim: number;
    sum_total_selesai: number;
    sum_total_batal: number;
    sum_total_overtime: number;
    sum_total_bbro: number;
}

export interface UseCalendarOptions {
    revalidateInterval?: number;
}

export interface UseCalendarReturn {
    data: Ref<CalendarApiResponse | null>;
    loading: Ref<boolean>;
    error: Ref<string | null>;
    revalidate: () => Promise<void>;
}

export function useCalendar(
    store_slug: string,
    month: string,
    options: UseCalendarOptions = {}
): UseCalendarReturn {
    const { revalidateInterval = 3000 } = options;
    
    const data = ref<CalendarApiResponse | null>(null);
    const loading = ref<boolean>(false);
    const error = ref<string | null>(null);
    
    let intervalId: number | null = null;

    const fetchCalendarData = async (): Promise<void> => {
        try {
            loading.value = true;
            error.value = null;

            const response = await fetch(`/api/calendar/${store_slug}/${month}`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                },
                credentials: 'same-origin',
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            data.value = result;
        } catch (err) {
            error.value = err instanceof Error ? err.message : 'An error occurred while fetching calendar data';
            console.error('Calendar fetch error:', err);
        } finally {
            loading.value = false;
        }
    };

    const revalidate = async (): Promise<void> => {
        await fetchCalendarData();
    };

    const startInterval = (): void => {
        if (revalidateInterval && revalidateInterval > 0) {
            intervalId = window.setInterval(() => {
                fetchCalendarData();
            }, revalidateInterval);
        }
    };

    const stopInterval = (): void => {
        if (intervalId) {
            clearInterval(intervalId);
            intervalId = null;
        }
    };

    onMounted(() => {
        fetchCalendarData();
        startInterval();
    });

    onUnmounted(() => {
        stopInterval();
    });

    return {
        data,
        loading,
        error,
        revalidate,
    };
}
